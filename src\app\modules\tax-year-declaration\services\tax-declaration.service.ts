import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Column, SeModal } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class TaxYearDeclarationService {
  constructor(private translateService: TranslateService) {
    // Empty constructor
  }

  getTableColumns(): Column[] {
    return [
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
        key: 'name',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
        cellConfig: {
          tooltip: true,
          ellipsis: true,
        },
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
        key: 'size',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
        key: 'docType',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
        key: 'description',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
    ];
  }

  getModalTableColumns(): Column[] {
    return [
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
        key: 'name',
        cellComponentName: 'defaultCellComponent',
        cellConfig: {
          tooltip: true,
          ellipsis: true,
        },
        resizable: false,
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
        key: 'size',
        size: 10,
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
        key: 'docType',
        cellComponentName: 'dropdownCellComponent',
        resizable: false,
        cellConfig: {
          id: 'docType',
          options: [
            {
              id: 1,
              label: this.translateService.instant(
                'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.MODEL_523_PANEL.NOM_DOCUMENT',
              ),
            },
          ],
          disabled: true,
          showClear: false,
          editable: false,
          autoSelect: true,
          readOnly: false,
          filter: false,
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: true,
        },
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
        key: 'description',
        cellComponentName: 'inputCellComponent',
        resizable: false,
        cellConfig: {
          id: 'docDescription',
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: true,
        },
      },
    ];
  }

  getWarningModalData(): SeModal {
    return {
      severity: 'warning',
      title: this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.WARNING_MODAL.TITLE',
      ),
      subtitle: `<p>${this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.WARNING_MODAL.DESCRIPTION',
      )}</p><p>${this.translateService.instant(
        'SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.WARNING_MODAL.QUESTION',
      )}</p>`,
      secondaryButton: true,
      secondaryButtonLabel: this.translateService.instant('UI_COMPONENTS.BUTTONS.CANCEL'),
      closableLabel: this.translateService.instant('UI_COMPONENTS.BUTTONS.CONTINUE'),
      closable: true,
      size: 'lg',
    };
  }
}
